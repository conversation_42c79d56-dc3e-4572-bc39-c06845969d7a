#!/usr/bin/env python3
"""
Проверка всех ролей админов
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories.user_repository import UserRepository
from database.repositories.student_repository import StudentRepository
from database.repositories.curator_repository import CuratorRepository
from database.repositories.teacher_repository import TeacherRepository
from database.repositories.manager_repository import ManagerRepository


async def check_admin_roles():
    """Проверить все роли админов"""
    print("\n👑 Проверка ролей админов")
    print("=" * 50)
    
    await init_database()
    
    # ID админов
    admin_ids = [955518340, 5205775566]
    admin_names = {
        955518340: "Андрей Климов",
        5205775566: "Мадияр Сапаров"
    }
    
    for admin_telegram_id in admin_ids:
        expected_name = admin_names[admin_telegram_id]
        print(f"\n👤 {expected_name} (ID: {admin_telegram_id})")
        print("-" * 40)
        
        # Получаем пользователя
        user = await UserRepository.get_by_telegram_id(admin_telegram_id)
        if not user:
            print(f"❌ Пользователь не найден")
            continue
        
        print(f"🆔 User ID: {user.id}")
        print(f"👤 Имя в БД: {user.name}")
        print(f"🏷️ Основная роль: {user.role}")
        
        # Проверяем роль студента
        student = await StudentRepository.get_by_user_id(user.id)
        if student:
            print(f"🎓 Студент: ✅ (ID: {student.id}, тариф: {student.tariff}, баллы: {student.points})")
            if student.group:
                print(f"   📚 Группа: {student.group.name}")
        else:
            print(f"🎓 Студент: ❌")
        
        # Проверяем роль куратора
        curator = await CuratorRepository.get_by_user_id(user.id)
        if curator:
            print(f"👨‍🎓 Куратор: ✅ (ID: {curator.id})")
            if curator.course:
                print(f"   📖 Курс: {curator.course.name}")
            if curator.subject:
                print(f"   📚 Предмет: {curator.subject.name}")
            
            # Проверяем группы куратора
            groups = await CuratorRepository.get_curator_groups(curator.id)
            if groups:
                print(f"   👥 Группы: {', '.join([g.name for g in groups])}")
            else:
                print(f"   👥 Группы: нет")
        else:
            print(f"👨‍🎓 Куратор: ❌")
        
        # Проверяем роль преподавателя
        teacher = await TeacherRepository.get_by_user_id(user.id)
        if teacher:
            print(f"👨‍🏫 Преподаватель: ✅ (ID: {teacher.id})")
            if teacher.course:
                print(f"   📖 Курс: {teacher.course.name}")
            if teacher.subject:
                print(f"   📚 Предмет: {teacher.subject.name}")
            if teacher.group:
                print(f"   👥 Группа: {teacher.group.name}")
        else:
            print(f"👨‍🏫 Преподаватель: ❌")
        
        # Проверяем роль менеджера
        manager = await ManagerRepository.get_by_user_id(user.id)
        if manager:
            print(f"👔 Менеджер: ✅ (ID: {manager.id})")
        else:
            print(f"👔 Менеджер: ❌")
    
    print(f"\n🎉 Проверка завершена!")


if __name__ == "__main__":
    asyncio.run(check_admin_roles())
