#!/usr/bin/env python3
"""
Отладка множественного выбора групп
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories import GroupRepository, SubjectRepository
from admin.utils.common import get_groups_selection_kb


async def debug_multiselect():
    """Отладка множественного выбора"""
    print("🔍 Отладка множественного выбора групп...")
    
    # Инициализируем базу данных
    await init_database()
    
    # Получаем первый предмет с группами
    subjects = await SubjectRepository.get_all()
    if not subjects:
        print("❌ Нет предметов в базе данных")
        return
    
    subject = subjects[0]
    groups = await GroupRepository.get_by_subject(subject.id)
    
    if not groups:
        print("❌ Нет групп для предмета")
        return
    
    print(f"📚 Предмет: {subject.name} (ID: {subject.id})")
    print(f"📋 Группы предмета:")
    for group in groups:
        print(f"  - {group.name} (ID: {group.id})")
    
    # Тестируем клавиатуру с пустым выбором
    print("\n1️⃣ Клавиатура с пустым выбором:")
    kb_empty = await get_groups_selection_kb([], subject.id)
    
    print("Кнопки:")
    for i, row in enumerate(kb_empty.inline_keyboard):
        for j, button in enumerate(row):
            print(f"  [{i}][{j}] '{button.text}' -> '{button.callback_data}'")
    
    # Тестируем клавиатуру с выбранными группами
    if len(groups) >= 2:
        selected_ids = [groups[0].id, groups[1].id]
        print(f"\n2️⃣ Клавиатура с выбранными группами {selected_ids}:")
        kb_selected = await get_groups_selection_kb(selected_ids, subject.id)
        
        print("Кнопки:")
        for i, row in enumerate(kb_selected.inline_keyboard):
            for j, button in enumerate(row):
                print(f"  [{i}][{j}] '{button.text}' -> '{button.callback_data}'")
    
    # Проверяем, какие callback_data должны обрабатываться
    print("\n3️⃣ Ожидаемые обработчики:")
    print("  - select_group_* -> select_group_for_curator")
    print("  - unselect_group_* -> unselect_group_for_curator") 
    print("  - finish_group_selection -> finish_group_selection_for_curator")
    
    print("\n🎉 Отладка завершена!")


if __name__ == "__main__":
    asyncio.run(debug_multiselect())
