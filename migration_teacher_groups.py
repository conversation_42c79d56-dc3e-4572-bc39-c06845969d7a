#!/usr/bin/env python3
"""
Миграция для добавления таблицы teacher_groups (many-to-many связь между учителями и группами)
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database, get_db_session
from database.models import Teacher, Group, teacher_groups
from sqlalchemy import text, select
from sqlalchemy.orm import selectinload


async def migrate_teacher_groups():
    """Миграция для создания таблицы teacher_groups и перенос данных"""
    print("🔄 Начинаем миграцию teacher_groups...")
    
    # Инициализируем базу данных
    await init_database()
    
    async with get_db_session() as session:
        # 1. Создаем таблицу teacher_groups если её нет
        try:
            await session.execute(text("""
                CREATE TABLE IF NOT EXISTS teacher_groups (
                    teacher_id INTEGER NOT NULL,
                    group_id INTEGER NOT NULL,
                    PRIMARY KEY (teacher_id, group_id),
                    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
                    FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE
                )
            """))
            print("✅ Таблица teacher_groups создана")
        except Exception as e:
            print(f"⚠️ Ошибка при создании таблицы teacher_groups: {e}")
        
        # 2. Переносим данные из поля group_id в таблицу teacher_groups
        try:
            # Получаем всех учителей с их группами
            result = await session.execute(
                select(Teacher).options(selectinload(Teacher.groups))
            )
            teachers = result.scalars().all()
            
            migrated_count = 0
            for teacher in teachers:
                # Проверяем, есть ли у учителя старое поле group_id
                if hasattr(teacher, 'group_id') and teacher.group_id:
                    # Проверяем, не добавлена ли уже эта связь
                    existing = await session.execute(
                        text("SELECT 1 FROM teacher_groups WHERE teacher_id = :teacher_id AND group_id = :group_id"),
                        {"teacher_id": teacher.id, "group_id": teacher.group_id}
                    )
                    
                    if not existing.scalar():
                        # Добавляем связь в таблицу teacher_groups
                        await session.execute(
                            text("INSERT INTO teacher_groups (teacher_id, group_id) VALUES (:teacher_id, :group_id)"),
                            {"teacher_id": teacher.id, "group_id": teacher.group_id}
                        )
                        migrated_count += 1
                        print(f"✅ Перенесена связь: учитель {teacher.id} -> группа {teacher.group_id}")
            
            await session.commit()
            print(f"✅ Перенесено {migrated_count} связей учитель-группа")
            
        except Exception as e:
            print(f"⚠️ Ошибка при переносе данных: {e}")
            await session.rollback()
        
        # 3. Удаляем старое поле group_id из таблицы teachers
        try:
            # Проверяем, существует ли колонка group_id
            result = await session.execute(text("PRAGMA table_info(teachers)"))
            columns = result.fetchall()
            has_group_id = any(col[1] == 'group_id' for col in columns)
            
            if has_group_id:
                # Создаем новую таблицу без поля group_id
                await session.execute(text("""
                    CREATE TABLE teachers_new (
                        id INTEGER PRIMARY KEY,
                        user_id INTEGER NOT NULL UNIQUE,
                        course_id INTEGER,
                        subject_id INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE SET NULL,
                        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL
                    )
                """))
                
                # Копируем данные (без group_id)
                await session.execute(text("""
                    INSERT INTO teachers_new (id, user_id, course_id, subject_id, created_at)
                    SELECT id, user_id, course_id, subject_id, created_at FROM teachers
                """))
                
                # Удаляем старую таблицу и переименовываем новую
                await session.execute(text("DROP TABLE teachers"))
                await session.execute(text("ALTER TABLE teachers_new RENAME TO teachers"))
                
                await session.commit()
                print("✅ Удалено поле group_id из таблицы teachers")
            else:
                print("✅ Поле group_id уже отсутствует в таблице teachers")
                
        except Exception as e:
            print(f"⚠️ Ошибка при удалении поля group_id: {e}")
            await session.rollback()
    
    print("🎉 Миграция teacher_groups завершена!")


if __name__ == "__main__":
    asyncio.run(migrate_teacher_groups())
