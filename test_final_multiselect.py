#!/usr/bin/env python3
"""
Финальный тест множественного выбора групп для кураторов и учителей
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database, get_db_session
from database.repositories import CuratorRepository, TeacherRepository, GroupRepository, UserRepository, SubjectRepository
from admin.utils.common import add_curator, add_teacher
from sqlalchemy import delete
from database.models import User


async def test_final_multiselect():
    """Финальный тест множественного выбора групп"""
    print("🎯 Финальный тест множественного выбора групп...")
    
    # Инициализируем базу данных
    await init_database()
    
    # Получаем первый предмет с группами
    subjects = await SubjectRepository.get_all()
    test_subject = None
    test_groups = []
    
    for subject in subjects:
        groups = await GroupRepository.get_by_subject(subject.id)
        if len(groups) >= 2:
            test_subject = subject
            test_groups = groups[:3]  # Берем первые 3 группы
            break
    
    if not test_subject or len(test_groups) < 2:
        print("❌ Недостаточно групп для тестирования")
        return
    
    print(f"📚 Тестируем с предметом: {test_subject.name}")
    print(f"📋 Выбранные группы для теста:")
    for group in test_groups:
        print(f"  - {group.name} (ID: {group.id})")
    
    group_ids = [group.id for group in test_groups]
    
    # Тестируем добавление куратора с множественными группами
    print("\n1️⃣ Тестируем добавление куратора с множественными группами...")
    curator_success = await add_curator(
        name="Тест Куратор Мультигрупп",
        telegram_id=888888001,
        course_id=1,
        subject_id=test_subject.id,
        group_ids=group_ids
    )
    
    if curator_success:
        print("✅ Куратор успешно добавлен")
        
        # Проверяем связи
        user = await UserRepository.get_by_telegram_id(888888001)
        curator = await CuratorRepository.get_by_user_id(user.id)
        curator_groups = await CuratorRepository.get_curator_groups(curator.id)
        
        print(f"📊 Куратор добавлен в {len(curator_groups)} групп:")
        for group in curator_groups:
            print(f"  ✅ {group.name}")
        
        if len(curator_groups) == len(group_ids):
            print("✅ Все группы успешно связаны с куратором")
        else:
            print("❌ Не все группы связаны с куратором")
    else:
        print("❌ Ошибка при добавлении куратора")
    
    # Тестируем добавление учителя с множественными группами
    print("\n2️⃣ Тестируем добавление учителя с множественными группами...")
    teacher_success = await add_teacher(
        name="Тест Учитель Мультигрупп",
        telegram_id=888888002,
        course_id=1,
        subject_id=test_subject.id,
        group_ids=group_ids
    )
    
    if teacher_success:
        print("✅ Учитель успешно добавлен")
        
        # Проверяем связи
        user = await UserRepository.get_by_telegram_id(888888002)
        teacher = await TeacherRepository.get_by_user_id(user.id)
        teacher_groups = await TeacherRepository.get_teacher_groups(teacher.id)
        
        print(f"📊 Учитель добавлен в {len(teacher_groups)} групп:")
        for group in teacher_groups:
            print(f"  ✅ {group.name}")
        
        if len(teacher_groups) == len(group_ids):
            print("✅ Все группы успешно связаны с учителем")
        else:
            print("❌ Не все группы связаны с учителем")
    else:
        print("❌ Ошибка при добавлении учителя")
    
    print("\n🎉 Финальный тест завершен!")
    
    # Очистка тестовых данных
    print("\n🧹 Очищаем тестовые данные...")
    async with get_db_session() as session:
        for telegram_id in [888888001, 888888002]:
            result = await session.execute(
                delete(User).where(User.telegram_id == telegram_id)
            )
            if result.rowcount > 0:
                print(f"✅ Удален тестовый пользователь {telegram_id}")
        await session.commit()
    print("✅ Очистка завершена")


if __name__ == "__main__":
    asyncio.run(test_final_multiselect())
