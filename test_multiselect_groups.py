#!/usr/bin/env python3
"""
Тестовый скрипт для проверки множественного выбора групп для кураторов и учителей
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories import CuratorRepository, TeacherRepository, GroupRepository, UserRepository
from admin.utils.common import add_curator, add_teacher


async def test_multiselect_groups():
    """Тестируем множественный выбор групп"""
    print("🧪 Тестируем множественный выбор групп...")
    
    # Инициализируем базу данных
    await init_database()
    
    # Получаем доступные группы
    groups = await GroupRepository.get_all()
    if len(groups) < 2:
        print("❌ Недостаточно групп для тестирования (нужно минимум 2)")
        return
    
    print(f"📋 Доступные группы:")
    for group in groups:
        print(f"  - ID: {group.id}, Название: {group.name}")
    
    # Выбираем первые 2 группы для тестирования
    test_group_ids = [groups[0].id, groups[1].id]
    print(f"🎯 Тестируем с группами: {test_group_ids}")
    
    # Тестируем добавление куратора с множественными группами
    print("\n1️⃣ Тестируем добавление куратора...")
    curator_success = await add_curator(
        name="Тестовый Куратор",
        telegram_id=999999001,
        course_id=1,  # Используем фиксированный ID курса
        subject_id=groups[0].subject_id,
        group_ids=test_group_ids
    )
    
    if curator_success:
        print("✅ Куратор успешно добавлен")
        
        # Проверяем, что куратор добавлен во все группы
        user = await UserRepository.get_by_telegram_id(999999001)
        if user:
            curator = await CuratorRepository.get_by_user_id(user.id)
            if curator:
                curator_groups = await CuratorRepository.get_curator_groups(curator.id)
                print(f"📊 Куратор добавлен в {len(curator_groups)} групп:")
                for group in curator_groups:
                    print(f"  - {group.name}")
            else:
                print("❌ Профиль куратора не найден")
        else:
            print("❌ Пользователь куратора не найден")
    else:
        print("❌ Ошибка при добавлении куратора")
    
    # Тестируем добавление учителя с множественными группами
    print("\n2️⃣ Тестируем добавление учителя...")
    teacher_success = await add_teacher(
        name="Тестовый Учитель",
        telegram_id=999999002,
        course_id=1,  # Используем фиксированный ID курса
        subject_id=groups[0].subject_id,
        group_ids=test_group_ids
    )
    
    if teacher_success:
        print("✅ Учитель успешно добавлен")
        
        # Проверяем, что учитель добавлен во все группы
        user = await UserRepository.get_by_telegram_id(999999002)
        if user:
            teacher = await TeacherRepository.get_by_user_id(user.id)
            if teacher:
                teacher_groups = await TeacherRepository.get_teacher_groups(teacher.id)
                print(f"📊 Учитель добавлен в {len(teacher_groups)} групп:")
                for group in teacher_groups:
                    print(f"  - {group.name}")
            else:
                print("❌ Профиль учителя не найден")
        else:
            print("❌ Пользователь учителя не найден")
    else:
        print("❌ Ошибка при добавлении учителя")
    
    print("\n🎉 Тестирование завершено!")


async def cleanup_test_data():
    """Очищаем тестовые данные"""
    print("🧹 Очищаем тестовые данные...")

    from database import get_db_session
    from sqlalchemy import delete
    from database.models import User

    # Удаляем тестовых пользователей
    test_telegram_ids = [999999001, 999999002]

    async with get_db_session() as session:
        for telegram_id in test_telegram_ids:
            result = await session.execute(
                delete(User).where(User.telegram_id == telegram_id)
            )
            if result.rowcount > 0:
                print(f"✅ Удален тестовый пользователь {telegram_id}")

        await session.commit()

    print("✅ Очистка завершена")


if __name__ == "__main__":
    print("Выберите действие:")
    print("1. Запустить тест")
    print("2. Очистить тестовые данные")
    
    choice = input("Введите номер (1 или 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_multiselect_groups())
    elif choice == "2":
        asyncio.run(cleanup_test_data())
    else:
        print("❌ Неверный выбор")
