#!/usr/bin/env python3
"""
Тестовый скрипт для проверки интерфейса множественного выбора групп
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories import GroupRepository, SubjectRepository
from admin.utils.common import get_groups_selection_kb


async def test_multiselect_interface():
    """Тестируем интерфейс множественного выбора"""
    print("🧪 Тестируем интерфейс множественного выбора групп...")
    
    # Инициализируем базу данных
    await init_database()
    
    # Получаем первый предмет для тестирования
    subjects = await SubjectRepository.get_all()
    if not subjects:
        print("❌ Нет предметов в базе данных")
        return
    
    subject = subjects[0]
    print(f"📚 Тестируем с предметом: {subject.name} (ID: {subject.id})")
    
    # Получаем группы этого предмета
    groups = await GroupRepository.get_by_subject(subject.id)
    if not groups:
        print("❌ Нет групп для этого предмета")
        return
    
    print(f"📋 Доступные группы:")
    for group in groups:
        print(f"  - ID: {group.id}, Название: {group.name}")
    
    # Тестируем клавиатуру с пустым выбором
    print("\n1️⃣ Тестируем клавиатуру с пустым выбором:")
    kb_empty = await get_groups_selection_kb([], subject.id)
    print(f"✅ Клавиатура создана с {len(kb_empty.inline_keyboard)} кнопками")
    
    # Проверяем структуру кнопок
    for i, row in enumerate(kb_empty.inline_keyboard):
        for j, button in enumerate(row):
            print(f"  Кнопка [{i}][{j}]: '{button.text}' -> '{button.callback_data}'")
    
    # Тестируем клавиатуру с выбранными группами
    if len(groups) >= 2:
        selected_ids = [groups[0].id, groups[1].id]
        print(f"\n2️⃣ Тестируем клавиатуру с выбранными группами: {selected_ids}")
        kb_selected = await get_groups_selection_kb(selected_ids, subject.id)
        print(f"✅ Клавиатура создана с {len(kb_selected.inline_keyboard)} кнопками")
        
        # Проверяем структуру кнопок
        for i, row in enumerate(kb_selected.inline_keyboard):
            for j, button in enumerate(row):
                print(f"  Кнопка [{i}][{j}]: '{button.text}' -> '{button.callback_data}'")
    
    print("\n🎉 Тестирование интерфейса завершено!")


async def test_callback_data_parsing():
    """Тестируем парсинг callback_data"""
    print("\n🔍 Тестируем парсинг callback_data...")
    
    test_callbacks = [
        "select_group_5",
        "unselect_group_10", 
        "finish_group_selection"
    ]
    
    for callback_data in test_callbacks:
        print(f"\nCallback: '{callback_data}'")
        
        if callback_data.startswith("select_group_"):
            group_id = int(callback_data.replace("select_group_", ""))
            print(f"  ✅ Выбор группы ID: {group_id}")
        elif callback_data.startswith("unselect_group_"):
            group_id = int(callback_data.replace("unselect_group_", ""))
            print(f"  ❌ Отмена выбора группы ID: {group_id}")
        elif callback_data == "finish_group_selection":
            print(f"  ✅ Завершение выбора")
        else:
            print(f"  ❓ Неизвестный callback")


if __name__ == "__main__":
    # Запускаем все тесты автоматически
    asyncio.run(test_multiselect_interface())
    asyncio.run(test_callback_data_parsing())
