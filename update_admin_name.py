#!/usr/bin/env python3
"""
Обновление имени второго админа
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database, get_db_session
from database.repositories.user_repository import UserRepository
from sqlalchemy import update
from database.models import User


async def update_admin_name():
    """Обновить имя второго админа"""
    print("\n🔄 Обновление имени второго админа")
    print("=" * 40)
    
    await init_database()
    
    # Получаем пользователя
    user = await UserRepository.get_by_telegram_id(5205775566)
    if not user:
        print(f"❌ Пользователь с ID 5205775566 не найден")
        return
    
    print(f"👤 Найден пользователь: {user.name}")
    print(f"🆔 User ID: {user.id}")
    
    # Обновляем имя
    async with get_db_session() as session:
        result = await session.execute(
            update(User)
            .where(User.id == user.id)
            .values(name="Мадияр Сапаров")
        )
        await session.commit()
        
        if result.rowcount > 0:
            print(f"✅ Имя обновлено на 'Мадияр Сапаров'")
        else:
            print(f"❌ Не удалось обновить имя")
    
    # Проверяем обновление
    updated_user = await UserRepository.get_by_telegram_id(5205775566)
    if updated_user:
        print(f"🔍 Проверка: {updated_user.name}")
    
    print(f"🎉 Обновление завершено!")


if __name__ == "__main__":
    asyncio.run(update_admin_name())
